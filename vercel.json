{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_APP_URL": "@next_public_app_url", "DATABASE_URL": "@database_url", "DIRECT_DATABASE_URL": "@direct_database_url", "NEXT_PUBLIC_GEMINI_API_KEY": "@next_public_gemini_api_key"}, "build": {"env": {"DATABASE_URL": "@database_url", "DIRECT_DATABASE_URL": "@direct_database_url"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}}