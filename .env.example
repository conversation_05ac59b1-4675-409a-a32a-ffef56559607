# ViceForms Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# This was inserted by `prisma init`:
# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
DIRECT_DATABASE_URL="psql 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require'"

DATABASE_URL="psql 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require'"



NEXT_PUBLIC_GEMINI_API_KEY="AIzaSyAAeHV3IIMD01KuioUJBu-YS-FME67fQjk"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

