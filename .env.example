# ViceForms Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# This was inserted by `prisma init`:
# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
DIRECT_DATABASE_URL="postgresql://neondb_owner:<>.us-east-2.aws.neon.tech/neondb?sslmode=require&connect_timeout=15&connection_limit=20"

DATABASE_URL="postgresql://neondb_owner:<>-pooler.us-east-2.aws.neon.tech/neondb?sslmode=require&pgbouncer=true&connect_timeout=15&pool_timeout=15"



NEXT_PUBLIC_GEMINI_API_KEY="AIzaSyAAeHV3IIMD01KuioUJBu-YS-FME67fQjk"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

